<template>
  <mt-dialog
    ref="dialog"
    :header="header"
    :buttons="buttons"
    css-class="terminate-agreement-dialog"
    width="600px"
    height="360px"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div style="color: red;padding:0 0 20px 0;">协议终止后将无法生成金额明细且无法撤销，请谨慎操作！</div>
      <mt-form ref="formRef" :model="formData" :rules="rules">
        <mt-form-item prop="reason" :label="$t('终止原因')" label-style="top">
          <mt-input
            v-model="formData.reason"
            :placeholder="$t('请输入终止原因')"
            :rows="4"
            :max-length="500"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="associatedVoucherNumber" :label="$t('关联协议单号')" label-style="top">
          <mt-input
            v-model="formData.associatedVoucherNumber"
            :placeholder="$t('请输入关联协议单号')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  name: 'TerminateAgreementDialog',
  props: {
    modalData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        reason: '',
        associatedVoucherNumber: '',
        headerId: null
      },
      rules: {
        reason: [
          { required: true, message: this.$t('请输入终止原因'), trigger: 'blur' }
        ]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData?.title || this.$t('终止协议')
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show() // 显示弹窗
    // 初始化表单数据
    if (this.modalData && this.modalData.id) {
      this.formData.headerId = this.modalData.id
    }
  },
  methods: {
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    async confirm() {
      try {
        // 表单验证
        const valid = await this.$refs.formRef.validate()
        if (!valid) {
          return
        }

        this.$loading()

        // 调用终止协议接口
        const params = {
          headerId: this.formData.headerId,
          reason: this.formData.reason,
          associatedVoucherNumber: this.formData.associatedVoucherNumber
        }

        const res = await this.$API.rebateManagement.stopAgreement(params)

        if (res.code === 200) {
          this.$toast({
            content: this.$t('终止协议成功'),
            type: 'success'
          })
          this.$refs.dialog.ejsRef.hide()
          // 通知父组件刷新数据
          this.$emit('success-function', res.data)
        } else {
          this.$toast({
            content: res.msg || this.$t('终止协议失败'),
            type: 'error'
          })
        }
      } catch (error) {
        console.error('终止协议失败:', error)
        if (error) {
          this.$toast({
            content: error.msg || this.$t('终止协议失败'),
            type: 'error'
          })
        } else {
          this.$toast({
            content: this.$t('请完成必填项'),
            type: 'warning'
          })
        }

      } finally {
        this.$hloading()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px;
}

::v-deep .terminate-agreement-dialog {
  .mt-dialog-content {
    padding: 0;
  }

  .mt-form-item {
    margin-bottom: 20px;
  }

  .mt-textarea {
    width: 100%;
  }

  .mt-input {
    width: 100%;
  }
}
</style>
